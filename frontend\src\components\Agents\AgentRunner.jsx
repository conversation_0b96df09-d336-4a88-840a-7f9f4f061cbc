/**
 * Agent Runner component for executing AI agents.
 * 
 * Provides an interface to run the AI agent workflow with
 * keyword input and real-time status updates.
 */

import React, { useState } from 'react';
import apiService from '../../services/api-service';

const AgentRunner = ({ onComplete }) => {
  const [keywords, setKeywords] = useState('');
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState('');
  const [results, setResults] = useState(null);
  const [error, setError] = useState('');

  const predefinedKeywords = [
    'cryptocurrency, passive income, online business',
    'affiliate marketing, digital products, e-commerce',
    'real estate, investment, financial freedom',
    'content creation, social media, influencer marketing',
    'freelancing, remote work, digital nomad'
  ];

  const handleKeywordSelect = (selectedKeywords) => {
    setKeywords(selectedKeywords);
  };

  const validateKeywords = () => {
    if (!keywords.trim()) {
      setError('Please enter at least one keyword');
      return false;
    }
    
    const keywordArray = keywords.split(',').map(k => k.trim()).filter(k => k);
    if (keywordArray.length === 0) {
      setError('Please enter valid keywords separated by commas');
      return false;
    }
    
    if (keywordArray.length > 10) {
      setError('Maximum 10 keywords allowed');
      return false;
    }
    
    return true;
  };

  const handleRunAgents = async () => {
    if (!validateKeywords()) return;

    setLoading(true);
    setError('');
    setStatus('Initializing AI agents...');
    setResults(null);

    try {
      const keywordArray = keywords.split(',').map(k => k.trim()).filter(k => k);
      
      setStatus('🔍 Discovering opportunities...');
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate progress
      
      setStatus('✍️ Generating content...');
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStatus('📤 Distributing content...');
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStatus('📊 Analyzing performance...');
      const results = await apiService.runAgents(keywordArray);
      
      setResults(results);
      setStatus('✅ Agents completed successfully!');
      
      // Notify parent component
      if (onComplete) {
        onComplete();
      }
      
    } catch (error) {
      console.error('Agent execution failed:', error);
      setError(error.message || 'Failed to run agents. Please try again.');
      setStatus('');
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setKeywords('');
    setStatus('');
    setResults(null);
    setError('');
  };

  return (
    <div>
      <div className="form-group">
        <label className="form-label">
          Keywords for Opportunity Discovery
        </label>
        <textarea
          className="form-input"
          placeholder="Enter keywords separated by commas (e.g., cryptocurrency, passive income, online business)"
          value={keywords}
          onChange={(e) => {
            setKeywords(e.target.value);
            if (error) setError('');
          }}
          disabled={loading}
          rows={3}
          style={{ resize: 'vertical' }}
        />
        <small className="text-muted">
          Enter 1-10 keywords that describe opportunities you want to discover
        </small>
      </div>

      {/* Predefined keyword suggestions */}
      <div className="form-group">
        <label className="form-label">Quick Start Templates:</label>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>
          {predefinedKeywords.map((keywordSet, index) => (
            <button
              key={index}
              type="button"
              className="btn btn-secondary"
              onClick={() => handleKeywordSelect(keywordSet)}
              disabled={loading}
              style={{ 
                fontSize: '0.8rem', 
                padding: '0.5rem 0.75rem',
                whiteSpace: 'nowrap'
              }}
            >
              {keywordSet.split(',')[0].trim()}...
            </button>
          ))}
        </div>
      </div>

      {error && (
        <div className="status status-error">
          ❌ {error}
        </div>
      )}

      {status && (
        <div className="status status-info">
          {loading && <div className="spinner" style={{ marginRight: '0.5rem' }}></div>}
          {status}
        </div>
      )}

      <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem' }}>
        <button
          className="btn btn-primary"
          onClick={handleRunAgents}
          disabled={loading || !keywords.trim()}
          style={{ flex: 1 }}
        >
          {loading ? (
            <>
              <div className="spinner" style={{ marginRight: '0.5rem' }}></div>
              Running Agents...
            </>
          ) : (
            '🚀 Run AI Agents'
          )}
        </button>
        
        <button
          className="btn btn-secondary"
          onClick={handleClear}
          disabled={loading}
        >
          🗑️ Clear
        </button>
      </div>

      {results && (
        <div className="status status-success">
          <h4 style={{ margin: '0 0 1rem 0' }}>🎉 Agent Execution Results</h4>
          <div style={{ fontSize: '0.9rem' }}>
            <p><strong>Opportunities Found:</strong> {Array.isArray(results) ? results.length : 'Multiple'}</p>
            <p><strong>Content Generated:</strong> ✅ Articles and social posts created</p>
            <p><strong>Distribution:</strong> ✅ Content distributed to platforms</p>
            <p><strong>Analytics:</strong> ✅ Performance tracking initialized</p>
          </div>
          <small className="text-muted">
            Check the tables below for detailed results and analytics.
          </small>
        </div>
      )}
    </div>
  );
};

export default AgentRunner;
