/**
 * Authentication form component for login and registration.
 * 
 * This component provides a modern, user-friendly interface for
 * user authentication with proper validation and error handling.
 */

import React, { useState } from 'react';
import apiService from '../../services/api-service';

const AuthForm = ({ onAuthSuccess }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (error) setError('');
  };

  const validateForm = () => {
    if (!formData.username.trim()) {
      setError('Username is required');
      return false;
    }
    if (!formData.password.trim()) {
      setError('Password is required');
      return false;
    }
    if (!isLogin && !formData.email.trim()) {
      setError('Email is required for registration');
      return false;
    }
    if (!isLogin && formData.password.length < 6) {
      setError('Password must be at least 6 characters long');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      if (isLogin) {
        await apiService.login(formData.username, formData.password);
        setSuccess('Login successful!');
      } else {
        await apiService.register({
          username: formData.username,
          email: formData.email,
          password: formData.password,
        });
        setSuccess('Registration successful!');
      }
      
      // Call success callback after a brief delay to show success message
      setTimeout(() => {
        onAuthSuccess();
      }, 1000);
      
    } catch (error) {
      setError(error.message || `${isLogin ? 'Login' : 'Registration'} failed`);
    } finally {
      setLoading(false);
    }
  };

  const toggleMode = () => {
    setIsLogin(!isLogin);
    setError('');
    setSuccess('');
    setFormData({
      username: '',
      email: '',
      password: '',
    });
  };

  return (
    <div className="container">
      <div className="header">
        <h1>🚀 AI Agent System</h1>
        <p>Discover opportunities, create content, and monitor performance with AI</p>
      </div>
      
      <div className="card" style={{ maxWidth: '400px', margin: '2rem auto' }}>
        <div className="card-header">
          <h2 className="card-title">
            {isLogin ? 'Welcome Back' : 'Create Account'}
          </h2>
        </div>

        {error && (
          <div className="status status-error">
            ❌ {error}
          </div>
        )}

        {success && (
          <div className="status status-success">
            ✅ {success}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label className="form-label" htmlFor="username">
              Username
            </label>
            <input
              type="text"
              id="username"
              name="username"
              className="form-input"
              placeholder="Enter your username"
              value={formData.username}
              onChange={handleInputChange}
              disabled={loading}
              required
            />
          </div>

          {!isLogin && (
            <div className="form-group">
              <label className="form-label" htmlFor="email">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                name="email"
                className="form-input"
                placeholder="Enter your email"
                value={formData.email}
                onChange={handleInputChange}
                disabled={loading}
                required
              />
            </div>
          )}

          <div className="form-group">
            <label className="form-label" htmlFor="password">
              Password
            </label>
            <input
              type="password"
              id="password"
              name="password"
              className="form-input"
              placeholder="Enter your password"
              value={formData.password}
              onChange={handleInputChange}
              disabled={loading}
              required
              minLength={isLogin ? 1 : 6}
            />
            {!isLogin && (
              <small className="text-muted">
                Password must be at least 6 characters long
              </small>
            )}
          </div>

          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading}
            style={{ width: '100%', marginBottom: '1rem' }}
          >
            {loading ? (
              <>
                <div className="spinner" style={{ marginRight: '0.5rem' }}></div>
                {isLogin ? 'Signing In...' : 'Creating Account...'}
              </>
            ) : (
              isLogin ? 'Sign In' : 'Create Account'
            )}
          </button>
        </form>

        <div className="text-center">
          <p className="text-muted">
            {isLogin ? "Don't have an account?" : "Already have an account?"}
          </p>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={toggleMode}
            disabled={loading}
            style={{ marginTop: '0.5rem' }}
          >
            {isLogin ? 'Create Account' : 'Sign In'}
          </button>
        </div>
      </div>

      <div className="text-center text-muted" style={{ marginTop: '2rem' }}>
        <p>
          🤖 Powered by AI agents for opportunity discovery and content automation
        </p>
      </div>
    </div>
  );
};

export default AuthForm;
