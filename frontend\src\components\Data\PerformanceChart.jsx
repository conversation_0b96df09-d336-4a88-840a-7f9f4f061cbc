/**
 * Performance Chart component for visualizing content performance data.
 * 
 * Displays interactive charts showing views, clicks, conversions,
 * and other performance metrics across different platforms.
 */

import { useState, useMemo } from 'react';

const PerformanceChart = ({ data = [] }) => {
  const [selectedMetric, setSelectedMetric] = useState('views');
  const [chartType, setChartType] = useState('bar');

  // Process data for visualization
  const chartData = useMemo(() => {
    if (data.length === 0) return null;

    // Group data by platform
    const platformData = data.reduce((acc, item) => {
      const platform = item.platform || 'Unknown';
      if (!acc[platform]) {
        acc[platform] = {
          views: 0,
          clicks: 0,
          conversions: 0,
          count: 0
        };
      }
      acc[platform].views += item.views || 0;
      acc[platform].clicks += item.clicks || 0;
      acc[platform].conversions += item.conversions || 0;
      acc[platform].count += 1;
      return acc;
    }, {});

    return platformData;
  }, [data]);

  const calculateCTR = (clicks, views) => {
    return views > 0 ? ((clicks / views) * 100).toFixed(2) : '0.00';
  };

  const calculateConversionRate = (conversions, clicks) => {
    return clicks > 0 ? ((conversions / clicks) * 100).toFixed(2) : '0.00';
  };

  const getMetricColor = (metric) => {
    const colors = {
      views: '#3b82f6',
      clicks: '#10b981',
      conversions: '#f59e0b',
      ctr: '#8b5cf6',
      conversionRate: '#ef4444'
    };
    return colors[metric] || '#6b7280';
  };

  if (!chartData || Object.keys(chartData).length === 0) {
    return (
      <div className="text-center p-xl">
        <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
        <h3 className="text-muted">No Performance Data Yet</h3>
        <p className="text-muted">
          Performance data will appear here after your content is distributed and starts receiving traffic.
        </p>
      </div>
    );
  }

  return (
    <div>
      {/* Chart Controls */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '2rem',
        flexWrap: 'wrap',
        gap: '1rem'
      }}>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <label className="form-label" style={{ margin: 0 }}>Metric:</label>
          <select 
            className="form-input" 
            value={selectedMetric}
            onChange={(e) => setSelectedMetric(e.target.value)}
            style={{ width: 'auto', minWidth: '150px' }}
          >
            <option value="views">Views</option>
            <option value="clicks">Clicks</option>
            <option value="conversions">Conversions</option>
          </select>
        </div>
        
        <div className="text-muted">
          Total Platforms: {Object.keys(chartData).length}
        </div>
      </div>

      {/* Performance Summary Cards */}
      <div className="grid grid-3" style={{ marginBottom: '2rem' }}>
        {Object.entries(chartData).map(([platform, metrics]) => (
          <div key={platform} className="card" style={{ padding: '1rem' }}>
            <h4 style={{ margin: '0 0 1rem 0', color: 'var(--primary-color)' }}>
              {platform}
            </h4>
            
            <div style={{ display: 'grid', gap: '0.5rem' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span className="text-muted">Views:</span>
                <span style={{ fontWeight: 'bold', color: getMetricColor('views') }}>
                  {metrics.views.toLocaleString()}
                </span>
              </div>
              
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span className="text-muted">Clicks:</span>
                <span style={{ fontWeight: 'bold', color: getMetricColor('clicks') }}>
                  {metrics.clicks.toLocaleString()}
                </span>
              </div>
              
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span className="text-muted">Conversions:</span>
                <span style={{ fontWeight: 'bold', color: getMetricColor('conversions') }}>
                  {metrics.conversions.toLocaleString()}
                </span>
              </div>
              
              <hr style={{ margin: '0.5rem 0', border: 'none', borderTop: '1px solid var(--border-color)' }} />
              
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span className="text-muted">CTR:</span>
                <span style={{ fontWeight: 'bold', color: getMetricColor('ctr') }}>
                  {calculateCTR(metrics.clicks, metrics.views)}%
                </span>
              </div>
              
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span className="text-muted">Conv. Rate:</span>
                <span style={{ fontWeight: 'bold', color: getMetricColor('conversionRate') }}>
                  {calculateConversionRate(metrics.conversions, metrics.clicks)}%
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Simple Bar Chart Visualization */}
      <div className="card" style={{ padding: '1.5rem' }}>
        <h4 style={{ margin: '0 0 1.5rem 0' }}>
          {selectedMetric.charAt(0).toUpperCase() + selectedMetric.slice(1)} by Platform
        </h4>
        
        <div style={{ display: 'flex', alignItems: 'end', gap: '1rem', height: '200px' }}>
          {Object.entries(chartData).map(([platform, metrics]) => {
            const value = metrics[selectedMetric];
            const maxValue = Math.max(...Object.values(chartData).map(m => m[selectedMetric]));
            const height = maxValue > 0 ? (value / maxValue) * 150 : 0;
            
            return (
              <div key={platform} style={{ 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'center',
                flex: 1,
                minWidth: '80px'
              }}>
                <div style={{ 
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'end',
                  height: '150px',
                  marginBottom: '0.5rem'
                }}>
                  <div style={{
                    backgroundColor: getMetricColor(selectedMetric),
                    height: `${height}px`,
                    width: '40px',
                    borderRadius: '4px 4px 0 0',
                    transition: 'height 0.3s ease',
                    display: 'flex',
                    alignItems: 'start',
                    justifyContent: 'center',
                    paddingTop: '0.25rem',
                    color: 'white',
                    fontSize: '0.8rem',
                    fontWeight: 'bold'
                  }}>
                    {height > 20 && value.toLocaleString()}
                  </div>
                </div>
                
                <div style={{ 
                  fontSize: '0.8rem', 
                  fontWeight: '500',
                  textAlign: 'center',
                  color: 'var(--text-secondary)'
                }}>
                  {platform}
                </div>
                
                {height <= 20 && (
                  <div style={{ 
                    fontSize: '0.7rem', 
                    color: 'var(--text-muted)',
                    textAlign: 'center'
                  }}>
                    {value.toLocaleString()}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Performance Insights */}
      <div className="card" style={{ marginTop: '1rem' }}>
        <h4 style={{ margin: '0 0 1rem 0' }}>📈 Performance Insights</h4>
        <div style={{ display: 'grid', gap: '0.5rem' }}>
          {Object.entries(chartData).map(([platform, metrics]) => {
            const ctr = parseFloat(calculateCTR(metrics.clicks, metrics.views));
            const convRate = parseFloat(calculateConversionRate(metrics.conversions, metrics.clicks));
            
            let insight = '';
            if (ctr > 5) {
              insight = '🎯 Excellent click-through rate!';
            } else if (ctr > 2) {
              insight = '👍 Good engagement levels';
            } else if (metrics.views > 0) {
              insight = '💡 Consider improving headlines and CTAs';
            }
            
            if (convRate > 10) {
              insight += ' 🚀 Outstanding conversion rate!';
            } else if (convRate > 5) {
              insight += ' ✅ Solid conversion performance';
            }
            
            return insight ? (
              <div key={platform} style={{ 
                padding: '0.75rem',
                backgroundColor: 'var(--bg-secondary)',
                borderRadius: 'var(--border-radius)',
                fontSize: '0.9rem'
              }}>
                <strong>{platform}:</strong> {insight}
              </div>
            ) : null;
          })}
        </div>
      </div>
    </div>
  );
};

export default PerformanceChart;
