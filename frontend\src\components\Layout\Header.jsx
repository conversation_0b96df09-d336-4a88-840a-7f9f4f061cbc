/**
 * Header component for the AI Agent System dashboard.
 * 
 * Provides navigation, user info, and logout functionality.
 */

import React from 'react';

const Header = ({ onLogout }) => {
  const handleLogout = () => {
    if (window.confirm('Are you sure you want to logout?')) {
      onLogout();
    }
  };

  return (
    <div className="header">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h1 style={{ marginBottom: '0.5rem' }}>🚀 AI Agent Dashboard</h1>
          <p style={{ margin: 0, opacity: 0.9 }}>
            Automate opportunity discovery and content creation
          </p>
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <div style={{ textAlign: 'right' }}>
            <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>
              Welcome back!
            </div>
            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>
              {new Date().toLocaleDateString()}
            </div>
          </div>
          
          <button
            onClick={handleLogout}
            className="btn btn-secondary"
            style={{ 
              background: 'rgba(255, 255, 255, 0.2)',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              color: 'white'
            }}
          >
            🚪 Logout
          </button>
        </div>
      </div>
    </div>
  );
};

export default Header;
