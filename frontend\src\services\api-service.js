/**
 * API service for communicating with the AI Agent System backend.
 *
 * This module provides a centralized way to make API calls to the FastAPI backend,
 * with automatic token management and error handling.
 */

import axios from 'axios';

// Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000';
const TOKEN_KEY = 'ai_agent_token';

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add authentication token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(TOKEN_KEY);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem(TOKEN_KEY);
      window.location.reload(); // Force re-authentication
    }
    return Promise.reject(error);
  }
);

// API service methods
const apiService = {
  // Authentication
  async register(userData) {
    try {
      const response = await apiClient.post('/register/', userData);
      if (response.data.access_token) {
        localStorage.setItem(TOKEN_KEY, response.data.access_token);
      }
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Registration failed');
    }
  },

  async login(username, password) {
    try {
      const formData = new URLSearchParams();
      formData.append('username', username);
      formData.append('password', password);

      const response = await apiClient.post('/token/', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      if (response.data.access_token) {
        localStorage.setItem(TOKEN_KEY, response.data.access_token);
      }
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Login failed');
    }
  },

  logout() {
    localStorage.removeItem(TOKEN_KEY);
  },

  // Data fetching
  async getOpportunities() {
    try {
      const response = await apiClient.get('/opportunities/me/');
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to fetch opportunities');
    }
  },

  async getPerformance() {
    try {
      const response = await apiClient.get('/performance/me/');
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to fetch performance data');
    }
  },

  // Agent operations
  async runAgents(keywords) {
    try {
      const response = await apiClient.post('/run_agents/', keywords);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to run agents');
    }
  },

  // Utility methods
  handleError(error, defaultMessage) {
    const message = error.response?.data?.detail || defaultMessage;
    const status = error.response?.status;

    console.error('API Error:', {
      message,
      status,
      url: error.config?.url,
      method: error.config?.method,
    });

    return new Error(message);
  },

  isAuthenticated() {
    return !!localStorage.getItem(TOKEN_KEY);
  },

  getToken() {
    return localStorage.getItem(TOKEN_KEY);
  },
};

export default apiService;