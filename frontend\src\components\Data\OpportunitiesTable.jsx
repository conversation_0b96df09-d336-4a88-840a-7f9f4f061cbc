/**
 * Opportunities Table component for displaying discovered opportunities.
 * 
 * Shows a formatted table of opportunities with sorting, filtering,
 * and detailed information about each opportunity.
 */

import { useState, useMemo } from 'react';

const OpportunitiesTable = ({ data = [] }) => {
  const [sortField, setSortField] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');
  const [filterText, setFilterText] = useState('');

  // Sort and filter data
  const processedData = useMemo(() => {
    let filtered = data.filter(item => 
      item.niche?.toLowerCase().includes(filterText.toLowerCase()) ||
      item.source?.toLowerCase().includes(filterText.toLowerCase())
    );

    return filtered.sort((a, b) => {
      let aVal = a[sortField];
      let bVal = b[sortField];
      
      if (typeof aVal === 'string') {
        aVal = aVal.toLowerCase();
        bVal = bVal.toLowerCase();
      }
      
      if (sortDirection === 'asc') {
        return aVal > bVal ? 1 : -1;
      } else {
        return aVal < bVal ? 1 : -1;
      }
    });
  }, [data, sortField, sortDirection, filterText]);

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const getCpcColor = (cpc) => {
    if (cpc >= 3) return 'var(--success-color)';
    if (cpc >= 1.5) return 'var(--warning-color)';
    return 'var(--text-muted)';
  };

  if (data.length === 0) {
    return (
      <div className="text-center p-xl">
        <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🔍</div>
        <h3 className="text-muted">No Opportunities Yet</h3>
        <p className="text-muted">
          Run the AI agents to discover profitable opportunities in your chosen niches.
        </p>
      </div>
    );
  }

  return (
    <div>
      {/* Filter and Stats */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '1rem',
        flexWrap: 'wrap',
        gap: '1rem'
      }}>
        <div className="form-group" style={{ margin: 0, flex: 1, maxWidth: '300px' }}>
          <input
            type="text"
            className="form-input"
            placeholder="Filter opportunities..."
            value={filterText}
            onChange={(e) => setFilterText(e.target.value)}
          />
        </div>
        
        <div className="text-muted">
          Showing {processedData.length} of {data.length} opportunities
        </div>
      </div>

      {/* Table */}
      <div className="table-container">
        <table className="table">
          <thead>
            <tr>
              <th 
                onClick={() => handleSort('niche')}
                style={{ cursor: 'pointer', userSelect: 'none' }}
              >
                Opportunity Niche
                {sortField === 'niche' && (
                  <span style={{ marginLeft: '0.5rem' }}>
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th 
                onClick={() => handleSort('cpc')}
                style={{ cursor: 'pointer', userSelect: 'none' }}
              >
                CPC Value
                {sortField === 'cpc' && (
                  <span style={{ marginLeft: '0.5rem' }}>
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th 
                onClick={() => handleSort('source')}
                style={{ cursor: 'pointer', userSelect: 'none' }}
              >
                Source
                {sortField === 'source' && (
                  <span style={{ marginLeft: '0.5rem' }}>
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th 
                onClick={() => handleSort('created_at')}
                style={{ cursor: 'pointer', userSelect: 'none' }}
              >
                Discovered
                {sortField === 'created_at' && (
                  <span style={{ marginLeft: '0.5rem' }}>
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
            </tr>
          </thead>
          <tbody>
            {processedData.map((opportunity, index) => (
              <tr key={opportunity.id || index}>
                <td>
                  <div style={{ fontWeight: '500' }}>
                    {opportunity.niche}
                  </div>
                  {opportunity.summary && (
                    <div className="text-muted" style={{ fontSize: '0.9rem', marginTop: '0.25rem' }}>
                      {opportunity.summary.substring(0, 100)}
                      {opportunity.summary.length > 100 && '...'}
                    </div>
                  )}
                </td>
                <td>
                  <span 
                    style={{ 
                      fontWeight: 'bold',
                      color: getCpcColor(opportunity.cpc)
                    }}
                  >
                    {formatCurrency(opportunity.cpc)}
                  </span>
                </td>
                <td>
                  <span className="text-muted">
                    {opportunity.source || 'AI Discovery'}
                  </span>
                </td>
                <td className="text-muted">
                  {formatDate(opportunity.created_at)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default OpportunitiesTable;
