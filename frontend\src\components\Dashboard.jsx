/**
 * Main Dashboard component for the AI Agent Automation System.
 *
 * This component serves as the main interface for authenticated users,
 * providing access to all system features including agent management,
 * opportunity viewing, and performance monitoring.
 */

import React, { useEffect, useState } from 'react';
import AuthForm from './Auth/AuthForm';
import AgentRunner from './Agents/AgentRunner';
import OpportunitiesTable from './Data/OpportunitiesTable';
import PerformanceChart from './Data/PerformanceChart';
import Header from './Layout/Header';
import apiService from '../services/api-service';

const Dashboard = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [opportunities, setOpportunities] = useState([]);
  const [performance, setPerformance] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = () => {
    const isAuth = apiService.isAuthenticated();
    setIsAuthenticated(isAuth);

    if (isAuth) {
      fetchData();
    } else {
      setLoading(false);
    }
  };

  const fetchData = async () => {
    setLoading(true);
    setError('');

    try {
      const [opportunitiesData, performanceData] = await Promise.all([
        apiService.getOpportunities(),
        apiService.getPerformance()
      ]);

      setOpportunities(opportunitiesData || []);
      setPerformance(performanceData || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to load data. Please try refreshing the page.');

      // Handle authentication errors
      if (error.message.includes('401') || error.message.includes('unauthorized')) {
        handleLogout();
      }
    } finally {
      setLoading(false);
    }
  };

  const handleAuthSuccess = () => {
    setIsAuthenticated(true);
    fetchData();
  };

  const handleLogout = () => {
    apiService.logout();
    setIsAuthenticated(false);
    setOpportunities([]);
    setPerformance([]);
    setError('');
  };

  const handleAgentsComplete = () => {
    // Refresh data after agents complete
    fetchData();
  };

  // Show authentication form if not logged in
  if (!isAuthenticated) {
    return <AuthForm onAuthSuccess={handleAuthSuccess} />;
  }

  // Show loading state
  if (loading) {
    return (
      <div className="container">
        <Header onLogout={handleLogout} />
        <div className="card text-center">
          <div className="spinner" style={{ margin: '2rem auto' }}></div>
          <p className="text-muted">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  // Main dashboard view
  return (
    <div className="container">
      <Header onLogout={handleLogout} />

      {error && (
        <div className="status status-error">
          ❌ {error}
          <button
            className="btn btn-secondary"
            onClick={fetchData}
            style={{ marginLeft: '1rem' }}
          >
            Retry
          </button>
        </div>
      )}

      <div className="grid grid-2">
        {/* Agent Runner Section */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">🤖 AI Agents</h2>
          </div>
          <AgentRunner onComplete={handleAgentsComplete} />
        </div>

        {/* Quick Stats Section */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">📊 Quick Stats</h2>
          </div>
          <div className="grid grid-2">
            <div className="text-center">
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: 'var(--primary-color)' }}>
                {opportunities.length}
              </div>
              <div className="text-muted">Opportunities</div>
            </div>
            <div className="text-center">
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: 'var(--success-color)' }}>
                {performance.length}
              </div>
              <div className="text-muted">Content Items</div>
            </div>
          </div>
        </div>
      </div>

      {/* Opportunities Section */}
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">💰 Discovered Opportunities</h2>
          <button
            className="btn btn-secondary"
            onClick={fetchData}
            disabled={loading}
          >
            🔄 Refresh
          </button>
        </div>
        <OpportunitiesTable data={opportunities} />
      </div>

      {/* Performance Section */}
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">📈 Performance Analytics</h2>
        </div>
        <PerformanceChart data={performance} />
      </div>
    </div>
  );
};

export default Dashboard;